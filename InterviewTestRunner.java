import problemsolving.*;
import searching.*;
import sorting.*;
import strings.*;
import arrays.*;
import controlflow.*;
import datetime.*;
import exceptions.*;
import oop.*;

/**
 * Java Interview Test Runner
 * 
 * Comprehensive test runner for all Java interview coding concepts.
 * Run this class to see demonstrations of all implemented solutions.
 */
public class InterviewTestRunner {
    
    public static void main(String[] args) {
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                 JAVA INTERVIEW CODING COLLECTION            ║");
        System.out.println("║                                                              ║");
        System.out.println("║  Comprehensive Java coding problems for interview prep      ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();
        
        // Test each concept area
        testProblemSolving();
        testSearchingAlgorithms();
        testSortingAlgorithms();
        testStringHandling();
        testArraysAndCollections();
        testControlFlow();
        testDateTimeOperations();
        testExceptionHandling();
        testOOPPrinciples();
        
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                    TESTING COMPLETED                        ║");
        System.out.println("║                                                              ║");
        System.out.println("║  All Java interview concepts have been demonstrated!        ║");
        System.out.println("║  Check individual files for detailed implementations.       ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
    }
    
    private static void printSectionHeader(String title) {
        System.out.println("┌" + "─".repeat(60) + "┐");
        System.out.printf("│ %-58s │%n", title.toUpperCase());
        System.out.println("└" + "─".repeat(60) + "┘");
    }
    
    private static void printSectionFooter() {
        System.out.println("─".repeat(62));
        System.out.println();
    }
    
    private static void testProblemSolving() {
        printSectionHeader("Problem Solving & Mathematical Algorithms");
        
        try {
            System.out.println("Testing Fibonacci Solutions...");
            FibonacciSolutions.main(new String[]{});
            
            System.out.println("\nTesting Prime Number Problems...");
            PrimeNumberProblems.main(new String[]{});
            
            System.out.println("\nTesting Mathematical Puzzles...");
            MathematicalPuzzles.main(new String[]{});
            
        } catch (Exception e) {
            System.err.println("Error in Problem Solving tests: " + e.getMessage());
        }
        
        printSectionFooter();
    }
    
    private static void testSearchingAlgorithms() {
        printSectionHeader("Searching Algorithms");
        
        try {
            System.out.println("Testing Binary Search Problems...");
            BinarySearchProblems.main(new String[]{});
            
        } catch (Exception e) {
            System.err.println("Error in Searching tests: " + e.getMessage());
        }
        
        printSectionFooter();
    }
    
    private static void testSortingAlgorithms() {
        printSectionHeader("Sorting Algorithms");
        
        try {
            System.out.println("Testing Advanced Sorting Algorithms...");
            AdvancedSortingAlgorithms.main(new String[]{});
            
        } catch (Exception e) {
            System.err.println("Error in Sorting tests: " + e.getMessage());
        }
        
        printSectionFooter();
    }
    
    private static void testStringHandling() {
        printSectionHeader("String Handling & Manipulation");
        
        try {
            System.out.println("Testing String Basics...");
            StringBasics.main(new String[]{});
            
        } catch (Exception e) {
            System.err.println("Error in String tests: " + e.getMessage());
        }
        
        printSectionFooter();
    }
    
    private static void testArraysAndCollections() {
        printSectionHeader("Arrays & Collections Framework");
        
        try {
            System.out.println("Testing Array Algorithms...");
            ArrayAlgorithms.main(new String[]{});
            
        } catch (Exception e) {
            System.err.println("Error in Arrays tests: " + e.getMessage());
        }
        
        printSectionFooter();
    }
    
    private static void testControlFlow() {
        printSectionHeader("Control Flow Statements");
        
        try {
            System.out.println("Testing Control Flow Problems...");
            ControlFlowProblems.main(new String[]{});
            
        } catch (Exception e) {
            System.err.println("Error in Control Flow tests: " + e.getMessage());
        }
        
        printSectionFooter();
    }
    
    private static void testDateTimeOperations() {
        printSectionHeader("Date & Time Operations");
        
        try {
            System.out.println("Testing Date Time Problems...");
            DateTimeProblems.main(new String[]{});
            
        } catch (Exception e) {
            System.err.println("Error in Date/Time tests: " + e.getMessage());
        }
        
        printSectionFooter();
    }
    
    private static void testExceptionHandling() {
        printSectionHeader("Exception Handling");
        
        try {
            System.out.println("Testing Exception Handling Problems...");
            ExceptionHandlingProblems.main(new String[]{});
            
        } catch (Exception e) {
            System.err.println("Error in Exception Handling tests: " + e.getMessage());
        }
        
        printSectionFooter();
    }
    
    private static void testOOPPrinciples() {
        printSectionHeader("Object Oriented Programming");
        
        try {
            System.out.println("Testing OOP Principles...");
            OOPPrinciples.main(new String[]{});
            
        } catch (Exception e) {
            System.err.println("Error in OOP tests: " + e.getMessage());
        }
        
        printSectionFooter();
    }
}
